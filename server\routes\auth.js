const express = require("express");
const router = express.Router();
const credentials = require("../utils/credentials");

// Login route
router.post("/login", (req, res) => {
  const {username, password, role} = req.body;

  // Check admin credentials
  if (role === "admin") {
    if (
      username === credentials.admin.username &&
      password === credentials.admin.password
    ) {
      return res.status(200).json({
        success: true,
        user: {
          id: credentials.admin.id,
          username: credentials.admin.username,
          role: credentials.admin.role,
        },
      });
    }
  }

  // Check doctor credentials
  else if (role === "doctor") {
    const doctor = credentials.doctors.find(
      (doc) => doc.username === username && doc.password === password
    );

    if (doctor) {
      return res.status(200).json({
        success: true,
        user: {
          id: doctor.id,
          username: doctor.username,
          role: doctor.role,
          fullName: doctor.fullName,
        },
      });
    }
  }

  // Check patient credentials
  else if (role === "patient") {
    const patient = credentials.patients.find(
      (pat) => pat.username === username && pat.password === password
    );

    if (patient) {
      return res.status(200).json({
        success: true,
        user: {
          id: patient.id,
          username: patient.username,
          role: patient.role,
          fullName: patient.fullName,
        },
      });
    }
  }

  // Check caretaker credentials
  else if (role === "caretaker") {
    const caretaker = credentials.caretakers.find(
      (care) => care.username === username && care.password === password
    );

    if (caretaker) {
      return res.status(200).json({
        success: true,
        user: {
          id: caretaker.id,
          username: caretaker.username,
          role: caretaker.role,
          fullName: caretaker.fullName,
        },
      });
    }
  }

  // If no matching credentials found
  return res.status(401).json({
    success: false,
    message: "Invalid credentials",
  });
});

// Logout route
router.post("/logout", (req, res) => {
  // In a real application with sessions or JWT tokens,
  // you would invalidate the session or blacklist the token here

  return res.status(200).json({
    success: true,
    message: "Logged out successfully",
  });
});

module.exports = router;
