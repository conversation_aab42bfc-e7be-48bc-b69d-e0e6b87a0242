/* Nameplate Styles */
.nameplate-container {
  position: relative;
  max-width: 900px;
  width: 90%;
  margin: 0 auto;
  overflow: visible !important;
  z-index: 1;
  transform-style: preserve-3d;
  perspective: 1000px;
}

.nameplate-background {
  overflow: visible !important;
  border-width: 2px !important;
  box-shadow: 0 0 30px rgba(20, 184, 166, 0.8) !important;
  transform-style: preserve-3d;
}

.nameplate-content {
  position: relative;
  z-index: 2;
  overflow: visible !important;
}

/* Enhanced text glow effects */
.glow-text {
  color: var(--teal-400);
  text-shadow: 0 0 5px rgba(45, 212, 191, 0.5), 0 0 10px rgba(45, 212, 191, 0.3);
  position: relative;
  z-index: 2;
  letter-spacing: -0.02em;
  font-weight: 700;
}

.subtitle-glow {
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.15);
  letter-spacing: 0.01em;
}

/* Nameplate content styling */
.nameplate-content {
  position: relative;
  z-index: 2;
}

/* Fix for animation container */
.overflow-visible {
  overflow: visible !important;
}

/* Fix for the landing page container */
.landing-page-container {
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  width: 100%;
  height: 100%;
  overscroll-behavior: none;
  background-color: var(--dark-zinc-900);
}

/* Fix for the hero section */
.min-h-screen {
  min-height: 100vh;
  position: relative;
  overflow: visible !important;
}

/* Fix for the container */
.container {
  position: relative;
  overflow: visible !important;
}

/* Fix for the text */
.text-5xl,
.text-7xl,
.text-xl,
.text-2xl {
  position: relative;
  z-index: 2;
}

/* Fix for the button */
.btn-primary {
  position: relative;
  z-index: 2;
}

/* Add particles effect */
@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-20px) translateX(10px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.6;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

.particles-container {
  position: relative;
  overflow: hidden;
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  animation: pulse 10s infinite ease-in-out, float 15s infinite ease-in-out;
  opacity: 0.4;
  filter: blur(1px);
}

.particle-teal {
  background-color: var(--teal-400);
  box-shadow: 0 0 8px var(--teal-400);
}

.particle-orange {
  background-color: var(--orange-400);
  box-shadow: 0 0 8px var(--orange-400);
}

.rounded-2xl {
  border-radius: 1rem !important;
}
