.landing-page {
  min-height: 100vh;
}

/* Prevent unwanted selection */
.nameplate-container {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Fix scrollbar issues */
.landing-page-container {
  overflow-x: hidden;
  overflow-y: auto;
  overscroll-behavior: none;
  position: relative;
  width: 100%;
  height: 100%;
  touch-action: pan-y;
}

.hero {
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-xxl) 0;
  text-align: center;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  max-width: 800px;
  margin: 0 auto var(--spacing-xl);
  opacity: 0.9;
}

.hero-cta {
  background-color: white;
  color: var(--primary-color);
  font-weight: 600;
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

.hero-cta:hover {
  background-color: rgba(255, 255, 255, 0.9);
}

.section-title {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
  padding-bottom: var(--spacing-md);
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--primary-color);
}

.features {
  padding: var(--spacing-xxl) 0;
  background-color: white;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--spacing-lg);
}

@media (min-width: 640px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.feature-card {
  background-color: var(--background-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-lg);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
}

.feature-card h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
}

.about {
  padding: var(--spacing-xxl) 0;
  background-color: var(--background-color);
}

.about p {
  max-width: 800px;
  margin: 0 auto var(--spacing-md);
  text-align: center;
}

.footer {
  background-color: var(--text-primary-color);
  color: white;
  padding: var(--spacing-lg) 0;
  text-align: center;
}
