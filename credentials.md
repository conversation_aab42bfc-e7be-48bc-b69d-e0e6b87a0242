# Hospital Management System - Demo Credentials

This file contains the hardcoded credentials for the Hospital Management System. These credentials are for development and demonstration purposes only.

## Admin Credentials

- **Username**: admin
- **Password**: admin123
- **ID**: ADM001

## Doctor Credentials

### Doctor 1
- **Username**: doctor1
- **Password**: doc123
- **ID**: DOC001
- **Name**: Dr. <PERSON>
- **Specialization**: Cardiology

### Doctor 2
- **Username**: doctor2
- **Password**: doc456
- **ID**: DOC002
- **Name**: Dr. <PERSON>
- **Specialization**: Neurology

## Patient Credentials

### Patient 1
- **Username**: patient1
- **Password**: pat123
- **ID**: PAT001
- **Name**: <PERSON>
- **Assigned Doctor**: DOC001 (Dr. <PERSON>)
- **Assigned Caretaker**: CAR001 (<PERSON>)

### Patient 2
- **Username**: patient2
- **Password**: pat456
- **ID**: PAT002
- **Name**: <PERSON>
- **Assigned Doctor**: DOC002 (Dr. <PERSON>)
- **Assigned Caretaker**: CAR002 (<PERSON>)

## Caretaker Credentials

### Caretaker 1
- **Username**: caretaker1
- **Password**: care123
- **ID**: CAR001
- **Name**: Michael Brown
- **Assigned Patients**: PAT001 (Robert Williams)

### Caretaker 2
- **Username**: caretaker2
- **Password**: care456
- **ID**: CAR002
- **Name**: Jennifer Wilson
- **Assigned Patients**: PAT002 (Emily Davis)

## Note

In a production environment, these credentials would be stored securely in a database with proper encryption. For the purpose of this demonstration, they are hardcoded in the `server/utils/credentials.js` file.
