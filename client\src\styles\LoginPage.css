.login-page {
  min-height: calc(100vh - 70px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl) 0;
}

.login-container {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-lg);
  padding: var(--spacing-xl);
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.login-header h2 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.login-header p {
  color: var(--text-secondary-color);
}

.login-form {
  margin-bottom: var(--spacing-lg);
}

.login-button {
  width: 100%;
  padding: var(--spacing-md);
  margin-top: var(--spacing-md);
  font-weight: 600;
}

.login-help {
  border-top: 1px solid var(--surface-color);
  padding-top: var(--spacing-md);
  font-size: var(--font-size-sm);
  color: var(--text-secondary-color);
}

.login-help p {
  margin-bottom: var(--spacing-xs);
}
