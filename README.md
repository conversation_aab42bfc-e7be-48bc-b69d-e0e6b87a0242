# Hospital Management System

A comprehensive hospital management system built with the MERN stack (MongoDB, Express, React, Node.js). This system allows for management of doctors, patients, and caretakers with role-based access control.

## Features

- **Role-based Access Control**: Different dashboards for Admin, Doctor, Patient, and Caretaker
- **Admin Dashboard**: Manage doctors, patients, caretakers, and assignments
- **Doctor Dashboard**: View assigned patients, manage medical reports
- **Patient Dashboard**: View assigned doctors and caretakers, access medical reports
- **Caretaker Dashboard**: View assigned patients, access medical reports
- **Responsive Design**: Mobile-first approach for all devices
- **Modern UI**: Clean and professional interface with a medical-themed color scheme

## Tech Stack

- **Frontend**: React with React Router for navigation
- **Backend**: Node.js with Express
- **Database**: MongoDB (to be implemented)
- **Styling**: Custom CSS with responsive design

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Clone the repository
   ```
   git clone <repository-url>
   cd hospital-management-system
   ```

2. Install dependencies for the server
   ```
   cd server
   npm install
   ```

3. Install dependencies for the client
   ```
   cd ../client
   npm install
   ```

### Running the Application

1. Start the server
   ```
   cd server
   npm run dev
   ```

2. Start the client
   ```
   cd client
   npm run dev
   ```

3. Open your browser and navigate to `http://localhost:5173`

## Demo Credentials

### Admin
- Username: admin
- Password: admin123

### Doctor
- Username: doctor1
- Password: doc123

### Patient
- Username: patient1
- Password: pat123

### Caretaker
- Username: caretaker1
- Password: care123

## Project Structure

```
hospital-management-system/
├── client/                 # Frontend React application
│   ├── public/             # Public assets
│   └── src/
│       ├── components/     # Reusable UI components
│       ├── context/        # React context for state management
│       ├── pages/          # Page components
│       ├── styles/         # CSS files
│       └── utils/          # Utility functions
├── server/                 # Backend Express application
│   ├── controllers/        # Request handlers
│   ├── middleware/         # Custom middleware
│   ├── models/             # Data models (for future DB integration)
│   ├── routes/             # API routes
│   └── utils/              # Utility functions and hardcoded data
```

## Color Scheme

- Primary (Soft Blue): #4A90E2
- Secondary (Light Teal): #50E3C2
- Background (Almost White): #F9FAFB
- Cards/Surfaces (Light Gray): #E5E7EB
- Text Primary (Dark Slate): #1F2937
- Text Secondary (Cool Gray): #6B7280
- Accent (Calming Green): #A0D995

## Future Enhancements

- Database integration with MongoDB
- Authentication with JWT
- File upload for medical reports and images
- Appointment scheduling system
- Notification system
- Mobile application using React Native

## License

This project is licensed under the MIT License - see the LICENSE file for details.
