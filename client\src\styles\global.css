@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

/* <PERSON><PERSON> Font - Using system fonts as fallback */
@font-face {
  font-family: "Gilroy";
  src: local("Avenir"), local("Montserrat"), local("Segoe UI");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "Gilroy";
  src: local("Avenir"), local("Montserrat"), local("Segoe UI");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Gilroy";
  src: local("Avenir"), local("Montserrat"), local("Segoe UI");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "Gilroy";
  src: local("Avenir"), local("Montserrat"), local("Segoe UI");
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: "<PERSON>roy";
  src: local("Avenir"), local("Montserrat"), local("Segoe UI");
  font-weight: 700;
  font-style: normal;
}

:root {
  /* Color Scheme */
  --dark-zinc-900: #18181b;
  --dark-zinc-800: #27272a;
  --dark-zinc-700: #3f3f46;
  --zinc-100: #f4f4f5;
  --zinc-400: #a1a1aa;
  --teal-400: #2dd4bf;
  --teal-500: #14b8a6;
  --orange-400: #fb923c;
  --orange-500: #f97316;
  --emerald-500: #10b981;
  --red-500: #ef4444;
  --red-600: #dc2626;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

html,
body {
  overflow-x: hidden;
  position: relative;
  width: 100%;
  height: 100%;
  overscroll-behavior: none;
}

/* Customize scrollbars but allow scrolling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--dark-zinc-900);
}

::-webkit-scrollbar-thumb {
  background: var(--dark-zinc-700);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--teal-500);
}

body {
  font-family: "Gilroy", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  background-color: var(--dark-zinc-900);
  color: var(--zinc-100);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.2;
  font-family: "Space Grotesk", sans-serif;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.75rem;
}

p {
  margin-bottom: 1rem;
}

a {
  color: var(--teal-400);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--teal-500);
  text-decoration: underline;
}

/* Prevent link hover styles from affecting buttons */
a.btn:hover {
  text-decoration: none;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  width: auto;
}

.btn-primary {
  background-color: var(--teal-500);
  color: white;
  box-shadow: 0 0 15px rgba(20, 184, 166, 0.5);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: var(--teal-400);
  box-shadow: 0 0 25px rgba(20, 184, 166, 0.7);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
}

.btn-secondary {
  background-color: var(--dark-zinc-700);
  color: var(--zinc-100);
  border: 1px solid var(--teal-500);
}

.btn-secondary:hover {
  background-color: var(--dark-zinc-800);
}

.btn-danger {
  background-color: var(--red-500);
  color: white;
}

.btn-danger:hover {
  background-color: var(--red-600);
}

/* Cards */
.card {
  background-color: var(--dark-zinc-800);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--dark-zinc-700);
  transition: all 0.3s ease;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}

.card-hover:hover {
  box-shadow: 0 0 15px rgba(20, 184, 166, 0.5);
  border-color: var(--teal-500);
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: var(--zinc-100);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.5rem 0.75rem;
  background-color: var(--dark-zinc-700);
  border: 1px solid var(--dark-zinc-700);
  border-radius: 0.375rem;
  color: var(--zinc-100);
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--teal-500);
}

/* Alerts */
.alert {
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid var(--emerald-500);
  color: var(--emerald-500);
}

.alert-error {
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--red-500);
  color: var(--red-500);
}

.alert-info {
  background-color: rgba(20, 184, 166, 0.1);
  border: 1px solid var(--teal-500);
  color: var(--teal-500);
}

/* Navbar */
.navbar {
  background-color: var(--dark-zinc-800);
  border-bottom: 1px solid var(--dark-zinc-700);
  position: sticky;
  top: 0;
  z-index: 50;
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.navbar-logo {
  color: var(--zinc-100);
  text-decoration: none;
}

.navbar-links {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.navbar-link {
  color: var(--zinc-100);
  text-decoration: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.navbar-link:hover {
  background-color: var(--dark-zinc-700);
  color: var(--teal-400);
  text-decoration: none;
}

.navbar-user {
  color: var(--zinc-400);
  margin-right: 1rem;
}

.navbar-logout {
  background-color: transparent;
  color: var(--zinc-100);
  border: 1px solid var(--dark-zinc-700);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}

.navbar-logout:hover {
  background-color: var(--dark-zinc-700);
  border-color: var(--teal-500);
}

/* Utilities */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
  box-sizing: border-box;
}

.glow-text {
  color: var(--teal-400);
  text-shadow: 0 0 8px rgba(45, 212, 191, 0.7);
}

.glow-border {
  border: 1px solid var(--teal-500);
  box-shadow: 0 0 15px rgba(20, 184, 166, 0.5);
}

/* Responsive utilities */
.hidden {
  display: none;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.grid {
  display: grid;
  width: 100%;
  box-sizing: border-box;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.-z-10 {
  z-index: -10;
}

.overflow-visible {
  overflow: visible !important;
}

.rounded-2xl {
  border-radius: 1rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-8 > * + * {
  margin-top: 2rem;
}

.text-center {
  text-align: center;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-24 {
  margin-bottom: 6rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-4 {
  margin-right: 1rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.w-full {
  width: 100%;
}

.w-auto {
  width: auto;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.min-h-screen {
  min-height: 100vh;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

/* Media queries */
@media (min-width: 640px) {
  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (max-width: 639px) {
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .card {
    padding: 1.25rem;
  }

  .gap-8 {
    gap: 1.5rem;
  }
}

@media (min-width: 768px) {
  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:gap-10 {
    gap: 2.5rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
  }

  .md\:text-7xl {
    font-size: 4.5rem;
  }

  .md\:grid-cols-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }

  .md\:grid-cols-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }

  .md\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
}

@media (min-width: 1024px) {
  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:grid-cols-4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
  }

  .lg\:gap-12 {
    gap: 3rem;
  }
}
