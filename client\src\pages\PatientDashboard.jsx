import {useState, useEffect} from "react";
import {useAuth} from "../context/AuthContext";
import "../styles/DashboardCommon.css";

const PatientDashboard = () => {
  const {currentUser} = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [doctor, setDoctor] = useState(null);
  const [caretaker, setCaretaker] = useState(null);
  const [medicalReports, setMedicalReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch assigned doctor
        if (currentUser.assignedDoctor) {
          const doctorResponse = await fetch(
            `http://localhost:5000/api/users/doctor/${currentUser.assignedDoctor}`
          );
          const doctorData = await doctorResponse.json();
          setDoctor(doctorData);
        }

        // Fetch assigned caretaker
        if (currentUser.assignedCaretaker) {
          const caretakerResponse = await fetch(
            `http://localhost:5000/api/users/caretaker/${currentUser.assignedCaretaker}`
          );
          const caretakerData = await caretakerResponse.json();
          setCaretaker(caretakerData);
        }

        // Fetch medical reports
        const reportsResponse = await fetch(
          `http://localhost:5000/api/users/medical-reports/patient/${currentUser.id}`
        );
        const reportsData = await reportsResponse.json();
        setMedicalReports(reportsData);

        setLoading(false);
      } catch (err) {
        setError("Failed to fetch data. Please try again later.");
        setLoading(false);
        console.error(err);
      }
    };

    fetchData();
  }, [
    currentUser.id,
    currentUser.assignedDoctor,
    currentUser.assignedCaretaker,
  ]);

  return (
    <div className="patient-dashboard">
      <div className="container">
        <div className="dashboard-header">
          <h2>Patient Dashboard</h2>
          <p>Welcome, {currentUser.fullName || currentUser.username}</p>
        </div>

        {error && <div className="alert alert-error">{error}</div>}

        <div className="dashboard-tabs">
          <button
            className={activeTab === "overview" ? "active" : ""}
            onClick={() => setActiveTab("overview")}
          >
            Overview
          </button>
          <button
            className={activeTab === "medical-reports" ? "active" : ""}
            onClick={() => setActiveTab("medical-reports")}
          >
            Medical Reports
          </button>
          <button
            className={activeTab === "profile" ? "active" : ""}
            onClick={() => setActiveTab("profile")}
          >
            My Profile
          </button>
        </div>

        <div className="dashboard-content">
          {loading ? (
            <div className="loading">Loading data...</div>
          ) : (
            <>
              {/* Overview Tab */}
              {activeTab === "overview" && (
                <div className="overview-tab">
                  <div className="care-team-section">
                    <h3>Your Care Team</h3>

                    <div className="care-team-cards">
                      {doctor && (
                        <div className="care-team-card">
                          <div className="care-team-header">
                            <h4>Your Doctor</h4>
                          </div>
                          <div className="care-team-info">
                            <p>
                              <strong>Name:</strong> {doctor.fullName}
                            </p>
                            <p>
                              <strong>Specialization:</strong>{" "}
                              {doctor.professionalDetails.specialization}
                            </p>
                            <p>
                              <strong>Experience:</strong>{" "}
                              {doctor.professionalDetails.yearsOfExperience}{" "}
                              years
                            </p>
                            <p>
                              <strong>Contact:</strong>{" "}
                              {doctor.contactInfo.phone}
                            </p>
                            <p>
                              <strong>Email:</strong> {doctor.contactInfo.email}
                            </p>
                          </div>
                        </div>
                      )}

                      {caretaker && (
                        <div className="care-team-card">
                          <div className="care-team-header">
                            <h4>Your Caretaker</h4>
                          </div>
                          <div className="care-team-info">
                            <p>
                              <strong>Name:</strong> {caretaker.fullName}
                            </p>
                            <p>
                              <strong>Experience:</strong>{" "}
                              {caretaker.skillsAndExperience.yearsOfExperience}{" "}
                              years
                            </p>
                            <p>
                              <strong>Certifications:</strong>{" "}
                              {caretaker.skillsAndExperience.certifications.join(
                                ", "
                              )}
                            </p>
                            <p>
                              <strong>Contact:</strong>{" "}
                              {caretaker.contactInfo.phone}
                            </p>
                            <p>
                              <strong>Email:</strong>{" "}
                              {caretaker.contactInfo.email}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="health-summary-section">
                    <h3>Health Summary</h3>

                    <div className="health-summary-card">
                      <div className="health-summary-item">
                        <h4>Current Conditions</h4>
                        <ul>
                          {currentUser.healthInfo.currentConditions.map(
                            (condition, index) => (
                              <li key={index}>{condition}</li>
                            )
                          )}
                        </ul>
                      </div>

                      <div className="health-summary-item">
                        <h4>Allergies</h4>
                        <ul>
                          {currentUser.healthInfo.allergies.map(
                            (allergy, index) => (
                              <li key={index}>{allergy}</li>
                            )
                          )}
                        </ul>
                      </div>

                      <div className="health-summary-item">
                        <h4>Current Medications</h4>
                        <ul>
                          {currentUser.healthInfo.medications.map(
                            (medication, index) => (
                              <li key={index}>
                                {medication.name} - {medication.dosage}
                              </li>
                            )
                          )}
                        </ul>
                      </div>

                      <div className="health-summary-item">
                        <h4>Immunization Records</h4>
                        <ul>
                          {currentUser.healthInfo.immunizationRecords.map(
                            (record, index) => (
                              <li key={index}>{record}</li>
                            )
                          )}
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="recent-reports-section">
                    <h3>Recent Medical Reports</h3>

                    {medicalReports.length === 0 ? (
                      <div className="no-data">
                        No medical reports available.
                      </div>
                    ) : (
                      <div className="recent-reports-list">
                        {medicalReports.slice(0, 3).map((report) => (
                          <div className="report-card" key={report.id}>
                            <div className="report-header">
                              <h4>Report: {report.id}</h4>
                              <span className="report-date">
                                {new Date(report.date).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="report-content">
                              <p>
                                <strong>Diagnosis:</strong> {report.diagnosis}
                              </p>
                              <p>
                                <strong>Treatment:</strong> {report.treatment}
                              </p>
                              <p>
                                <strong>Notes:</strong> {report.notes}
                              </p>
                            </div>
                            <button className="button secondary">
                              View Full Report
                            </button>
                          </div>
                        ))}

                        {medicalReports.length > 3 && (
                          <button
                            className="view-all-button"
                            onClick={() => setActiveTab("medical-reports")}
                          >
                            View All Reports
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Medical Reports Tab */}
              {activeTab === "medical-reports" && (
                <div className="medical-reports-tab">
                  <h3>Your Medical Reports</h3>

                  {medicalReports.length === 0 ? (
                    <div className="no-data">No medical reports available.</div>
                  ) : (
                    <div className="reports-list">
                      {medicalReports.map((report) => (
                        <div className="report-card" key={report.id}>
                          <div className="report-header">
                            <h4>Report: {report.id}</h4>
                            <span className="report-date">
                              {new Date(report.date).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="report-content">
                            <p>
                              <strong>Diagnosis:</strong> {report.diagnosis}
                            </p>
                            <p>
                              <strong>Treatment:</strong> {report.treatment}
                            </p>
                            <p>
                              <strong>Notes:</strong> {report.notes}
                            </p>
                          </div>
                          <div className="report-actions">
                            <button className="button secondary">
                              Print Report
                            </button>
                            <button className="button">Request Update</button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="add-new">
                    <button className="button">Request New Report</button>
                  </div>
                </div>
              )}

              {/* Profile Tab */}
              {activeTab === "profile" && (
                <div className="profile-tab">
                  <h3>My Profile</h3>

                  <div className="profile-card">
                    <div className="profile-section">
                      <h4>Personal Information</h4>
                      <div className="profile-info">
                        <p>
                          <strong>Full Name:</strong> {currentUser.fullName}
                        </p>
                        <p>
                          <strong>ID:</strong> {currentUser.id}
                        </p>
                        <p>
                          <strong>Gender:</strong> {currentUser.gender}
                        </p>
                        <p>
                          <strong>Date of Birth:</strong>{" "}
                          {new Date(
                            currentUser.dateOfBirth
                          ).toLocaleDateString()}
                        </p>
                        <p>
                          <strong>Marital Status:</strong>{" "}
                          {currentUser.maritalStatus}
                        </p>
                      </div>
                    </div>

                    <div className="profile-section">
                      <h4>Contact Information</h4>
                      <div className="profile-info">
                        <p>
                          <strong>Email:</strong>{" "}
                          {currentUser.contactInfo.email}
                        </p>
                        <p>
                          <strong>Phone:</strong>{" "}
                          {currentUser.contactInfo.phone}
                        </p>
                        <p>
                          <strong>Address:</strong>{" "}
                          {`${currentUser.address.city}, ${currentUser.address.state} ${currentUser.address.zipCode}`}
                        </p>
                      </div>
                    </div>

                    <div className="profile-section">
                      <h4>Insurance Information</h4>
                      <div className="profile-info">
                        <p>
                          <strong>Provider:</strong>{" "}
                          {currentUser.insuranceDetails.provider}
                        </p>
                        <p>
                          <strong>Policy Number:</strong>{" "}
                          {currentUser.insuranceDetails.policyNumber}
                        </p>
                        <p>
                          <strong>Group Number:</strong>{" "}
                          {currentUser.insuranceDetails.groupNumber}
                        </p>
                      </div>
                    </div>

                    <div className="profile-section">
                      <h4>Emergency Contact</h4>
                      <div className="profile-info">
                        <p>
                          <strong>Name:</strong>{" "}
                          {currentUser.emergencyContact.name}
                        </p>
                        <p>
                          <strong>Relation:</strong>{" "}
                          {currentUser.emergencyContact.relation}
                        </p>
                        <p>
                          <strong>Phone:</strong>{" "}
                          {currentUser.emergencyContact.phone}
                        </p>
                      </div>
                    </div>

                    <div className="profile-actions">
                      <button className="button">Edit Profile</button>
                      <button className="button secondary">
                        Change Password
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default PatientDashboard;
