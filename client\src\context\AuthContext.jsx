import {createContext, useState, useContext, useEffect} from "react";

const AuthContext = createContext();

export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({children}) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is stored in localStorage
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      setCurrentUser(JSON.parse(storedUser));
    }
    setLoading(false);
  }, []);

  const login = async (username, password, role) => {
    try {
      // In a real app, this would be an API call
      const response = await fetch("http://localhost:5000/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({username, password, role}),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Login failed");
      }

      // Save user to state and localStorage
      setCurrentUser(data.user);
      localStorage.setItem("user", JSON.stringify(data.user));
      return data.user;
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Call the logout API endpoint
      await fetch("http://localhost:5000/api/auth/logout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        // In a real app with JWT, you would send the token here
        body: JSON.stringify({userId: currentUser?.id}),
      });

      // Clear user from state and localStorage
      setCurrentUser(null);
      localStorage.removeItem("user");
    } catch (error) {
      console.error("Logout error:", error);
      // Still remove the user from local state even if the API call fails
      setCurrentUser(null);
      localStorage.removeItem("user");
    }
  };

  const value = {
    currentUser,
    login,
    logout,
    isAdmin: currentUser?.role === "admin",
    isDoctor: currentUser?.role === "doctor",
    isPatient: currentUser?.role === "patient",
    isCaretaker: currentUser?.role === "caretaker",
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
