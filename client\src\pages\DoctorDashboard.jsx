import {useState, useEffect} from "react";
import {useAuth} from "../context/AuthContext";
import "../styles/DashboardCommon.css";

const DoctorDashboard = () => {
  const {currentUser} = useAuth();
  const [activeTab, setActiveTab] = useState("patients");
  const [patients, setPatients] = useState([]);
  const [caretakers, setCaretakers] = useState([]);
  const [medicalReports, setMedicalReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch assigned patients
        const assignmentsResponse = await fetch(
          `http://localhost:5000/api/users/assignments/doctor/${currentUser.id}`
        );
        const assignmentsData = await assignmentsResponse.json();

        // Get patient details for each assignment
        const patientPromises = assignmentsData.map((assignment) =>
          fetch(
            `http://localhost:5000/api/users/patient/${assignment.patientId}`
          ).then((res) => res.json())
        );

        const patientData = await Promise.all(patientPromises);
        setPatients(patientData);

        // Fetch caretakers
        const caretakersResponse = await fetch(
          "http://localhost:5000/api/users/caretakers"
        );
        const caretakersData = await caretakersResponse.json();
        setCaretakers(caretakersData);

        // Fetch medical reports
        const reportsResponse = await fetch(
          `http://localhost:5000/api/users/medical-reports`
        );
        const reportsData = await reportsResponse.json();

        // Filter reports for this doctor's patients
        const doctorReports = reportsData.filter(
          (report) => report.doctorId === currentUser.id
        );
        setMedicalReports(doctorReports);

        setLoading(false);
      } catch (err) {
        setError("Failed to fetch data. Please try again later.");
        setLoading(false);
        console.error(err);
      }
    };

    fetchData();
  }, [currentUser.id]);

  // Function to get patient name by ID
  const getPatientName = (id) => {
    const patient = patients.find((pat) => pat.id === id);
    return patient ? patient.fullName : "Unknown";
  };

  // Function to get caretaker name by ID
  const getCaretakerName = (id) => {
    const caretaker = caretakers.find((care) => care.id === id);
    return caretaker ? caretaker.fullName : "Unknown";
  };

  return (
    <div className="doctor-dashboard">
      <div className="container">
        <div className="dashboard-header">
          <h2>Doctor Dashboard</h2>
          <p>Welcome, Dr. {currentUser.fullName || currentUser.username}</p>
        </div>

        {error && <div className="alert alert-error">{error}</div>}

        <div className="dashboard-tabs">
          <button
            className={activeTab === "patients" ? "active" : ""}
            onClick={() => setActiveTab("patients")}
          >
            My Patients
          </button>
          <button
            className={activeTab === "reports" ? "active" : ""}
            onClick={() => setActiveTab("reports")}
          >
            Medical Reports
          </button>
          <button
            className={activeTab === "profile" ? "active" : ""}
            onClick={() => setActiveTab("profile")}
          >
            My Profile
          </button>
        </div>

        <div className="dashboard-content">
          {loading ? (
            <div className="loading">Loading data...</div>
          ) : (
            <>
              {/* Patients Tab */}
              {activeTab === "patients" && (
                <div className="patients-tab">
                  <h3>My Patients</h3>

                  {patients.length === 0 ? (
                    <div className="no-data">
                      No patients assigned to you yet.
                    </div>
                  ) : (
                    <div className="patient-cards">
                      {patients.map((patient) => (
                        <div className="patient-card" key={patient.id}>
                          <div className="patient-header">
                            <h4>{patient.fullName}</h4>
                            <span className="patient-id">ID: {patient.id}</span>
                          </div>

                          <div className="patient-info">
                            <p>
                              <strong>Age:</strong>{" "}
                              {new Date().getFullYear() -
                                new Date(
                                  patient.dateOfBirth
                                ).getFullYear()}{" "}
                              years
                            </p>
                            <p>
                              <strong>Gender:</strong> {patient.gender}
                            </p>
                            <p>
                              <strong>Contact:</strong>{" "}
                              {patient.contactInfo.phone}
                            </p>
                            <p>
                              <strong>Caretaker:</strong>{" "}
                              {getCaretakerName(patient.assignedCaretaker)}
                            </p>
                          </div>

                          <div className="patient-health">
                            <h5>Health Information</h5>
                            <p>
                              <strong>Conditions:</strong>{" "}
                              {patient.healthInfo.currentConditions.join(", ")}
                            </p>
                            <p>
                              <strong>Allergies:</strong>{" "}
                              {patient.healthInfo.allergies.join(", ")}
                            </p>
                            <p>
                              <strong>Medications:</strong>{" "}
                              {patient.healthInfo.medications
                                .map((med) => `${med.name} (${med.dosage})`)
                                .join(", ")}
                            </p>
                          </div>

                          <div className="patient-actions">
                            <button className="button">
                              View Full Details
                            </button>
                            <button className="button secondary">
                              Add Medical Report
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Medical Reports Tab */}
              {activeTab === "reports" && (
                <div className="reports-tab">
                  <h3>Medical Reports</h3>

                  {medicalReports.length === 0 ? (
                    <div className="no-data">
                      No medical reports created yet.
                    </div>
                  ) : (
                    <div className="table-container">
                      <table>
                        <thead>
                          <tr>
                            <th>Report ID</th>
                            <th>Patient</th>
                            <th>Date</th>
                            <th>Diagnosis</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {medicalReports.map((report) => (
                            <tr key={report.id}>
                              <td>{report.id}</td>
                              <td>{getPatientName(report.patientId)}</td>
                              <td>
                                {new Date(report.date).toLocaleDateString()}
                              </td>
                              <td>{report.diagnosis}</td>
                              <td>
                                <button className="action-btn">View</button>
                                <button className="action-btn">Edit</button>
                                <button className="action-btn delete">
                                  Delete
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}

                  <div className="add-new">
                    <button className="button">Create New Report</button>
                  </div>
                </div>
              )}

              {/* Profile Tab */}
              {activeTab === "profile" && (
                <div className="profile-tab">
                  <h3>My Profile</h3>

                  <div className="profile-card">
                    <div className="profile-section">
                      <h4>Personal Information</h4>
                      <div className="profile-info">
                        <p>
                          <strong>Full Name:</strong> {currentUser.fullName}
                        </p>
                        <p>
                          <strong>ID:</strong> {currentUser.id}
                        </p>
                        <p>
                          <strong>Gender:</strong> {currentUser.gender}
                        </p>
                        <p>
                          <strong>Date of Birth:</strong>{" "}
                          {new Date(
                            currentUser.dateOfBirth
                          ).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    <div className="profile-section">
                      <h4>Contact Information</h4>
                      <div className="profile-info">
                        <p>
                          <strong>Email:</strong>{" "}
                          {currentUser.contactInfo.email}
                        </p>
                        <p>
                          <strong>Phone:</strong>{" "}
                          {currentUser.contactInfo.phone}
                        </p>
                        <p>
                          <strong>Address:</strong>{" "}
                          {`${currentUser.address.city}, ${currentUser.address.state} ${currentUser.address.zipCode}`}
                        </p>
                      </div>
                    </div>

                    <div className="profile-section">
                      <h4>Professional Details</h4>
                      <div className="profile-info">
                        <p>
                          <strong>License Number:</strong>{" "}
                          {currentUser.professionalDetails.licenseNumber}
                        </p>
                        <p>
                          <strong>Specialization:</strong>{" "}
                          {currentUser.professionalDetails.specialization}
                        </p>
                        <p>
                          <strong>Experience:</strong>{" "}
                          {currentUser.professionalDetails.yearsOfExperience}{" "}
                          years
                        </p>
                        <p>
                          <strong>Qualifications:</strong>{" "}
                          {currentUser.professionalDetails.qualifications.join(
                            ", "
                          )}
                        </p>
                        <p>
                          <strong>Status:</strong>{" "}
                          {currentUser.professionalDetails.registrationStatus}
                        </p>
                      </div>
                    </div>

                    <div className="profile-section">
                      <h4>Employment Details</h4>
                      <div className="profile-info">
                        <p>
                          <strong>Current Hospital:</strong>{" "}
                          {currentUser.employmentDetails.currentHospital}
                        </p>
                        <p>
                          <strong>Work Schedule:</strong>{" "}
                          {currentUser.employmentDetails.workSchedule}
                        </p>
                        <p>
                          <strong>Employment Type:</strong>{" "}
                          {currentUser.employmentDetails.employmentType}
                        </p>
                      </div>
                    </div>

                    <div className="profile-actions">
                      <button className="button">Edit Profile</button>
                      <button className="button secondary">
                        Change Password
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default DoctorDashboard;
