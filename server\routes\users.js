const express = require('express');
const router = express.Router();
const credentials = require('../utils/credentials');

// Get all doctors
router.get('/doctors', (req, res) => {
  // Return doctors without sensitive information like passwords
  const doctors = credentials.doctors.map(doctor => {
    const { password, ...doctorData } = doctor;
    return doctorData;
  });
  
  res.status(200).json(doctors);
});

// Get all patients
router.get('/patients', (req, res) => {
  // Return patients without sensitive information like passwords
  const patients = credentials.patients.map(patient => {
    const { password, ...patientData } = patient;
    return patientData;
  });
  
  res.status(200).json(patients);
});

// Get all caretakers
router.get('/caretakers', (req, res) => {
  // Return caretakers without sensitive information like passwords
  const caretakers = credentials.caretakers.map(caretaker => {
    const { password, ...caretakerData } = caretaker;
    return caretakerData;
  });
  
  res.status(200).json(caretakers);
});

// Get user by ID and role
router.get('/:role/:id', (req, res) => {
  const { role, id } = req.params;
  
  let user = null;
  
  if (role === 'doctor') {
    user = credentials.doctors.find(doc => doc.id === id);
  } else if (role === 'patient') {
    user = credentials.patients.find(pat => pat.id === id);
  } else if (role === 'caretaker') {
    user = credentials.caretakers.find(care => care.id === id);
  }
  
  if (user) {
    // Remove password before sending
    const { password, ...userData } = user;
    return res.status(200).json(userData);
  }
  
  return res.status(404).json({ message: 'User not found' });
});

// Get assignments
router.get('/assignments', (req, res) => {
  res.status(200).json(credentials.assignments);
});

// Get assignments by patient ID
router.get('/assignments/patient/:id', (req, res) => {
  const { id } = req.params;
  const assignments = credentials.assignments.filter(assign => assign.patientId === id);
  
  if (assignments.length > 0) {
    return res.status(200).json(assignments);
  }
  
  return res.status(404).json({ message: 'No assignments found for this patient' });
});

// Get assignments by doctor ID
router.get('/assignments/doctor/:id', (req, res) => {
  const { id } = req.params;
  const assignments = credentials.assignments.filter(assign => assign.doctorId === id);
  
  if (assignments.length > 0) {
    return res.status(200).json(assignments);
  }
  
  return res.status(404).json({ message: 'No assignments found for this doctor' });
});

// Get assignments by caretaker ID
router.get('/assignments/caretaker/:id', (req, res) => {
  const { id } = req.params;
  const assignments = credentials.assignments.filter(assign => assign.caretakerId === id);
  
  if (assignments.length > 0) {
    return res.status(200).json(assignments);
  }
  
  return res.status(404).json({ message: 'No assignments found for this caretaker' });
});

// Get medical reports
router.get('/medical-reports', (req, res) => {
  res.status(200).json(credentials.medicalReports);
});

// Get medical reports by patient ID
router.get('/medical-reports/patient/:id', (req, res) => {
  const { id } = req.params;
  const reports = credentials.medicalReports.filter(report => report.patientId === id);
  
  if (reports.length > 0) {
    return res.status(200).json(reports);
  }
  
  return res.status(404).json({ message: 'No medical reports found for this patient' });
});

module.exports = router;
