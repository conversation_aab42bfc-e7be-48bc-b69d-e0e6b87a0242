import {useState, useEffect} from "react";
import {useNavigate, useParams} from "react-router-dom";
import {useAuth} from "../context/AuthContext";
import {motion} from "framer-motion";
import {
  FiUser,
  FiUsers,
  FiActivity,
  FiShield,
  FiPlus,
  FiEdit,
  FiTrash2,
  <PERSON>Eye,
  FiBarChart2,
} from "react-icons/fi";
import Modal from "../components/Modal";
import DoctorForm from "../components/DoctorForm";
import PatientForm from "../components/PatientForm";
import CaretakerForm from "../components/CaretakerForm";
import AssignmentForm from "../components/AssignmentForm";
import "../styles/AdminDashboardNew.css";

const AdminDashboard = () => {
  const {currentUser} = useAuth();
  const navigate = useNavigate();
  const {tab} = useParams();
  const [activeTab, setActiveTab] = useState(tab || "overview");

  // Modal states
  const [isDoctorModalOpen, setIsDoctorModalOpen] = useState(false);
  const [isPatientModalOpen, setIsPatientModalOpen] = useState(false);
  const [isCaretakerModalOpen, setIsCaretakerModalOpen] = useState(false);
  const [isAssignmentModalOpen, setIsAssignmentModalOpen] = useState(false);

  // Update URL when tab changes
  const handleTabChange = (newTab) => {
    setActiveTab(newTab);
    if (newTab !== "overview") {
      navigate(`/admin/${newTab}`);
    } else {
      navigate("/admin");
    }
  };

  // Sync with URL params when they change
  useEffect(() => {
    if (tab) {
      setActiveTab(tab);
    }
  }, [tab]);

  // Form submission handlers
  const handleDoctorSubmit = (formData) => {
    console.log("Doctor form submitted:", formData);
    // Here you would typically make an API call to save the doctor
    // For now, we'll just close the modal
    setIsDoctorModalOpen(false);
  };

  const handlePatientSubmit = (formData) => {
    console.log("Patient form submitted:", formData);
    setIsPatientModalOpen(false);
  };

  const handleCaretakerSubmit = (formData) => {
    console.log("Caretaker form submitted:", formData);
    setIsCaretakerModalOpen(false);
  };

  const handleAssignmentSubmit = (formData) => {
    console.log("Assignment form submitted:", formData);
    setIsAssignmentModalOpen(false);
  };
  const [doctors, setDoctors] = useState([]);
  const [patients, setPatients] = useState([]);
  const [caretakers, setCaretakers] = useState([]);
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch doctors
        const doctorsResponse = await fetch(
          "http://localhost:5000/api/users/doctors"
        );
        const doctorsData = await doctorsResponse.json();
        setDoctors(doctorsData);

        // Fetch patients
        const patientsResponse = await fetch(
          "http://localhost:5000/api/users/patients"
        );
        const patientsData = await patientsResponse.json();
        setPatients(patientsData);

        // Fetch caretakers
        const caretakersResponse = await fetch(
          "http://localhost:5000/api/users/caretakers"
        );
        const caretakersData = await caretakersResponse.json();
        setCaretakers(caretakersData);

        // Fetch assignments
        const assignmentsResponse = await fetch(
          "http://localhost:5000/api/users/assignments"
        );
        const assignmentsData = await assignmentsResponse.json();
        setAssignments(assignmentsData);

        setLoading(false);
      } catch (err) {
        setError("Failed to fetch data. Please try again later.");
        setLoading(false);
        console.error(err);
      }
    };

    fetchData();
  }, []);

  // Function to get doctor name by ID
  const getDoctorName = (id) => {
    const doctor = doctors.find((doc) => doc.id === id);
    return doctor ? doctor.fullName : "Unknown";
  };

  // Function to get patient name by ID
  const getPatientName = (id) => {
    const patient = patients.find((pat) => pat.id === id);
    return patient ? patient.fullName : "Unknown";
  };

  // Function to get caretaker name by ID
  const getCaretakerName = (id) => {
    const caretaker = caretakers.find((care) => care.id === id);
    return caretaker ? caretaker.fullName : "Unknown";
  };

  return (
    <div className="admin-dashboard">
      <div className="admin-container">
        <motion.div
          className="dashboard-header"
          initial={{opacity: 0, y: -20}}
          animate={{opacity: 1, y: 0}}
          transition={{duration: 0.5}}
        >
          <h2>Admin Dashboard</h2>
          <p>Welcome, {currentUser.username || "Administrator"}</p>
        </motion.div>

        {error && <div className="alert alert-error">{error}</div>}

        <div className="dashboard-tabs">
          <button
            className={activeTab === "overview" ? "active" : ""}
            onClick={() => handleTabChange("overview")}
          >
            <FiBarChart2 size={20} />
            Overview
          </button>
          <button
            className={activeTab === "doctors" ? "active" : ""}
            onClick={() => handleTabChange("doctors")}
          >
            <FiUser size={20} />
            Doctors
          </button>
          <button
            className={activeTab === "patients" ? "active" : ""}
            onClick={() => handleTabChange("patients")}
          >
            <FiActivity size={20} />
            Patients
          </button>
          <button
            className={activeTab === "caretakers" ? "active" : ""}
            onClick={() => handleTabChange("caretakers")}
          >
            <FiUsers size={20} />
            Caretakers
          </button>
          <button
            className={activeTab === "assignments" ? "active" : ""}
            onClick={() => handleTabChange("assignments")}
          >
            <FiShield size={20} />
            Assignments
          </button>
        </div>

        <div
          className="dashboard-content"
          style={{
            overflow: "auto",
            maxHeight: "calc(100vh - 220px)",
            backgroundColor: "var(--dark-zinc-800)",
          }}
        >
          {loading ? (
            <div className="loading">Loading data...</div>
          ) : (
            <>
              {/* Overview Tab */}
              {activeTab === "overview" && (
                <div className="overview-tab" style={{overflow: "visible"}}>
                  <div className="stats-grid">
                    <motion.div
                      className="stat-card"
                      initial={{opacity: 0, y: 20}}
                      animate={{opacity: 1, y: 0}}
                      transition={{duration: 0.5, delay: 0.1}}
                    >
                      <div className="stat-icon">
                        <FiUser size={24} />
                      </div>
                      <h3>Total Doctors</h3>
                      <p className="stat-number">{doctors.length}</p>
                    </motion.div>
                    <motion.div
                      className="stat-card"
                      initial={{opacity: 0, y: 20}}
                      animate={{opacity: 1, y: 0}}
                      transition={{duration: 0.5, delay: 0.2}}
                    >
                      <div className="stat-icon">
                        <FiActivity size={24} />
                      </div>
                      <h3>Total Patients</h3>
                      <p className="stat-number">{patients.length}</p>
                    </motion.div>
                    <motion.div
                      className="stat-card"
                      initial={{opacity: 0, y: 20}}
                      animate={{opacity: 1, y: 0}}
                      transition={{duration: 0.5, delay: 0.3}}
                    >
                      <div className="stat-icon">
                        <FiUsers size={24} />
                      </div>
                      <h3>Total Caretakers</h3>
                      <p className="stat-number">{caretakers.length}</p>
                    </motion.div>
                    <motion.div
                      className="stat-card"
                      initial={{opacity: 0, y: 20}}
                      animate={{opacity: 1, y: 0}}
                      transition={{duration: 0.5, delay: 0.4}}
                    >
                      <div className="stat-icon">
                        <FiShield size={24} />
                      </div>
                      <h3>Total Assignments</h3>
                      <p className="stat-number">{assignments.length}</p>
                    </motion.div>
                  </div>
                </div>
              )}

              {/* Doctors Tab */}
              {activeTab === "doctors" && (
                <div className="doctors-tab">
                  <h3>Doctors List</h3>
                  <div className="table-container">
                    <table>
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Name</th>
                          <th>Specialization</th>
                          <th>Experience</th>
                          <th>Status</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {doctors.map((doctor) => (
                          <tr key={doctor.id}>
                            <td>{doctor.id}</td>
                            <td>{doctor.fullName}</td>
                            <td>{doctor.professionalDetails.specialization}</td>
                            <td>
                              {doctor.professionalDetails.yearsOfExperience}{" "}
                              years
                            </td>
                            <td>
                              <span
                                className={`badge ${
                                  doctor.professionalDetails
                                    .registrationStatus === "Active"
                                    ? "badge-accent"
                                    : "badge-secondary"
                                }`}
                              >
                                {doctor.professionalDetails.registrationStatus}
                              </span>
                            </td>
                            <td>
                              <button className="action-btn">
                                <FiEye /> View
                              </button>
                              <button className="action-btn">
                                <FiEdit /> Edit
                              </button>
                              <button className="action-btn delete">
                                <FiTrash2 /> Delete
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  <div className="add-new">
                    <button
                      className="button"
                      onClick={() => setIsDoctorModalOpen(true)}
                    >
                      <FiPlus size={20} /> Add New Doctor
                    </button>
                  </div>
                </div>
              )}

              {/* Patients Tab */}
              {activeTab === "patients" && (
                <div className="patients-tab">
                  <h3>Patients List</h3>
                  <div className="table-container">
                    <table>
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Name</th>
                          <th>Age</th>
                          <th>Doctor</th>
                          <th>Caretaker</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {patients.map((patient) => {
                          // Calculate age from date of birth
                          const birthDate = new Date(patient.dateOfBirth);
                          const today = new Date();
                          let age =
                            today.getFullYear() - birthDate.getFullYear();
                          const monthDiff =
                            today.getMonth() - birthDate.getMonth();
                          if (
                            monthDiff < 0 ||
                            (monthDiff === 0 &&
                              today.getDate() < birthDate.getDate())
                          ) {
                            age--;
                          }

                          return (
                            <tr key={patient.id}>
                              <td>{patient.id}</td>
                              <td>{patient.fullName}</td>
                              <td>{age} years</td>
                              <td>{getDoctorName(patient.assignedDoctor)}</td>
                              <td>
                                {getCaretakerName(patient.assignedCaretaker)}
                              </td>
                              <td>
                                <button className="action-btn">View</button>
                                <button className="action-btn">Edit</button>
                                <button className="action-btn delete">
                                  Delete
                                </button>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                  <div className="add-new">
                    <button
                      className="button"
                      onClick={() => setIsPatientModalOpen(true)}
                    >
                      <FiPlus size={20} /> Add New Patient
                    </button>
                  </div>
                </div>
              )}

              {/* Caretakers Tab */}
              {activeTab === "caretakers" && (
                <div className="caretakers-tab">
                  <h3>Caretakers List</h3>
                  <div className="table-container">
                    <table>
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Name</th>
                          <th>Experience</th>
                          <th>Certifications</th>
                          <th>Assigned Patients</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {caretakers.map((caretaker) => (
                          <tr key={caretaker.id}>
                            <td>{caretaker.id}</td>
                            <td>{caretaker.fullName}</td>
                            <td>
                              {caretaker.skillsAndExperience.yearsOfExperience}{" "}
                              years
                            </td>
                            <td>
                              {caretaker.skillsAndExperience.certifications.join(
                                ", "
                              )}
                            </td>
                            <td>{caretaker.assignedPatients.length}</td>
                            <td>
                              <button className="action-btn">View</button>
                              <button className="action-btn">Edit</button>
                              <button className="action-btn delete">
                                Delete
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  <div className="add-new">
                    <button
                      className="button"
                      onClick={() => setIsCaretakerModalOpen(true)}
                    >
                      <FiPlus size={20} /> Add New Caretaker
                    </button>
                  </div>
                </div>
              )}

              {/* Assignments Tab */}
              {activeTab === "assignments" && (
                <div className="assignments-tab">
                  <h3>Patient Assignments</h3>
                  <div className="table-container">
                    <table>
                      <thead>
                        <tr>
                          <th>Patient</th>
                          <th>Doctor</th>
                          <th>Caretaker</th>
                          <th>Start Date</th>
                          <th>End Date</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {assignments.map((assignment, index) => (
                          <tr key={index}>
                            <td>{getPatientName(assignment.patientId)}</td>
                            <td>{getDoctorName(assignment.doctorId)}</td>
                            <td>{getCaretakerName(assignment.caretakerId)}</td>
                            <td>
                              {new Date(
                                assignment.startDate
                              ).toLocaleDateString()}
                            </td>
                            <td>
                              {assignment.endDate
                                ? new Date(
                                    assignment.endDate
                                  ).toLocaleDateString()
                                : "Ongoing"}
                            </td>
                            <td>
                              <button className="action-btn">Edit</button>
                              <button className="action-btn delete">End</button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  <div className="add-new">
                    <button
                      className="button"
                      onClick={() => setIsAssignmentModalOpen(true)}
                    >
                      <FiPlus size={20} /> Create New Assignment
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Modals */}
      <Modal
        isOpen={isDoctorModalOpen}
        onClose={() => setIsDoctorModalOpen(false)}
        title="Add New Doctor"
      >
        <DoctorForm
          onSubmit={handleDoctorSubmit}
          onCancel={() => setIsDoctorModalOpen(false)}
        />
      </Modal>

      <Modal
        isOpen={isPatientModalOpen}
        onClose={() => setIsPatientModalOpen(false)}
        title="Add New Patient"
      >
        <PatientForm
          onSubmit={handlePatientSubmit}
          onCancel={() => setIsPatientModalOpen(false)}
        />
      </Modal>

      <Modal
        isOpen={isCaretakerModalOpen}
        onClose={() => setIsCaretakerModalOpen(false)}
        title="Add New Caretaker"
      >
        <CaretakerForm
          onSubmit={handleCaretakerSubmit}
          onCancel={() => setIsCaretakerModalOpen(false)}
        />
      </Modal>

      <Modal
        isOpen={isAssignmentModalOpen}
        onClose={() => setIsAssignmentModalOpen(false)}
        title="Create New Assignment"
      >
        <AssignmentForm
          onSubmit={handleAssignmentSubmit}
          onCancel={() => setIsAssignmentModalOpen(false)}
        />
      </Modal>
    </div>
  );
};

export default AdminDashboard;
