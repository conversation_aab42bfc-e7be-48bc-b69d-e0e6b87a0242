A hospital management system, where admin has to register the doctor, patient and caretaker. And provide them a unique id and password to login into their account.
The details which needed for the registration are as follows:
Fields for Doctor Registration
Personal Information

Full Name

Date of Birth

Gender

Contact Information (Phone Number, Email)

Address (City, State, Zip Code)

Professional Details

Medical License Number

Specialization (e.g., Psychiatry, Neurology)

Years of Experience

Qualifications (Degrees, Certifications)

Registration Status (Active/Inactive)

Employment Details

Current Hospital/Clinic Name

Work Schedule (Availability)

Employment Type (Full-time/Part-time)

Additional Information

Emergency Contact Information

Insurance Coverage (if applicable)

Fields for Patient Registration
Personal Information

Full Name

Date of Birth

Gender

Marital Status

Social Security Number (if applicable)

Contact Information (Phone Number, Email)

Address (City, State, Zip Code)

Health Information

Current Health Conditions

Past Medical History

Allergies

Medications (Name and Dosage)

Immunization Records

Insurance Details

Insurance Provider Name

Policy Number

Group Number

Specific health report if available

Emergency Contact

Name

Relation to Patient

Phone Number

Other Details

Primary Care Physician (if applicable)

Preferred Language

Fields for Caretaker Registration
Personal Information

Full Name

Date of Birth

Gender

Contact Information (Phone Number, Email)

Address (City, State, Zip Code)

Employment Details

Relationship to Patient (Family Member/Professional Caregiver)

Availability Schedule

Skills and Experience

Relevant Certifications/Training (e.g., First Aid, Nursing Assistance)

Years of Experience in Caregiving

Additional Information

Emergency Contact Information

Notes on Caregiving Preferences or Limitations

Assignment Fields for Patient-Doctor-Caretaker Mapping
Patient ID or Name.

Assigned Doctor ID or Name.

Assigned Caretaker ID or Name.

Start Date and End Date of Assignment.

Notes or Special Instructions for the Doctor/Caretaker.

the Admin can also update, delete and view the details of the registered users. and can also add new users.

now i want you to create the landing page, the admin panel page with best practice by hiding them from normal users , it should be protected and kept safe.you can create a route specifically for the admin to monitor.

Then the login page where we have option to login as doctor, patient and caretaker.
i just want all these things to be hard-coded for now. We will be implementing the database later. to login into the systems including the admin use hard-coded credentials for now and provide all in a file named credentials.

for the dashboard of admin display the buttons to add users, modify users, delete users and view users. provide a dashboard to monitor all the users and their details. the admin also has to assign the users to the doctor and caretaker.
caretaker can be assigned to multiple patients. doctor can be assigned to multiple patients.

for the dashboard of doctor display the list of patients assigned to him with their name and id and their details. caretaker assigned to patients with their id and name and other details. the doctor should also be able to see the medical report of the patient. doctor should be able to update the medical report of the patient, add new report and delete the report.the doctor can also assign the caretaker to the patient.

for the dashboard of caretaker display the list of patients assigned to him with their name and id and their details and the doctor assigned to them with their id and name. the caretaker should also be able to see the medical report of the patient. the caretaker can also caretaker should be able to update the medical report of the patient, add new report.

for the dashboard of patient display the name and id of caretaker assigned to him and their details. with the name and id of doctor assigned to them. the patient should also be able to see the medical report of their own. patient should be able to update the medical report of the themselves, add new report.

in terms of the privilages the doctor can change their own details, caretaker can change their own details, patient can change their own details.

The project is to be created in MERN tech stack.
This is a hospital project so keep it simple, professional yet attractive.

use best practices so that it can be scaled up and also follow mobile first approach so that it can be also used as PWA and make it responsive so that it can be used in all devices.
